@media (max-width: 600px) {
    .container {
      padding: 10px;
      font-size: 14px;
    }
  }
  
  .fc-business-heading-row{
    font-size: 3rem;
    font-weight: 600;
    color: black;
    margin-top:48px;
  }
  .fc--become-short-register{
    background-color: rgba(250, 206, 73, 0.1);
    padding: 30px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    gap:2rem;
    border-radius: 32px;
    input{    
      box-sizing: border-box;
      position: relative;
      width: 455px;
      height: 65px;    
      background: #FFFFFF;
      border-radius: 12px;
      border: none;
      padding:0px 1rem;
    }
    label{
      font-size:20px;
      color: rgba(0, 0, 0, 0.616);
      font-weight: 300;
    }
    span{
      font-size: 24px;
      color: black;
      font-weight: 500;
    }
  
      .become-register-btn {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 32px 64px;
        width: 180px;
        height: 57px;
        background: #014681;
        border-radius: 100px;
        border: none;
        color: white;
        font-size: 18px;
        font-weight: 600;
      }
  }
  
  
  .fc-content-card{
    align-items: center;
  
  
    img{
      max-width: 90%;
    }
  
    label{
      color: black;
    }
  
    h5{
      margin-bottom: 1rem;
    }
  
    p{
       line-height: 30px;
       color: black;
    }
  
    .read-more-btn{    
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 21px 46px;  
    width:max-content;
    height: 52px;
    background: #000000;
    border-radius: 200px;
    color: white;
    text-decoration: none;
    }
  }
  
  .fc-full-container{
    background: #12141D;
    position: relative;
    min-height: auto;
    overflow: hidden;
    padding-block:100px;
    display: flex;
    align-items: end;
    
    > div{
      z-index: 1;
      position: relative;
    }
    &::after{
      position: absolute;
      width: 447px;
      height: 443px;
      right:0px;
      top: 3px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }
    
    &::before{
      position: absolute;
      width: 464px;
      height: 468px;
      left: 0px;
      top: 742px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }    
  
  
    label{
      font-size: 28px;
      font-weight: 600;
    }
  
    p{
      font-size: 18px;
      line-height: 30px;
      font-weight: 300;
      margin-top: 1rem;
  
      b{
        color: #FFA500;
        font-weight: 600;
      }
    }
  }
  
  
  
  
  .fc-become-partner-section{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 140px auto;
    text-align: center;
    gap:2.5rem;
    flex-direction: column;
  
    // .label-text{
    // }
  
    .label-text{
      font-size: 24px;
      color: #014681;
    }
  
    h4{
      font-size: 38px;
      font-weight: 600;
      max-width: 875px;
      margin: 0px auto;
    }
    
    input[type="text"]{  
      width: 609.16px;
      height: 65px;
      background: #F4F4F4;
      border-radius: 12px;
      border: none ;
      padding: 0px 1rem;
    }
    .become-btn{
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;  
      gap: 10px;
      width: 253px;  
      height: 57px;
      background: #014681;
      border-radius: 100px;
      color: white;
      font-size: 18px;
      border: none;
      margin: 0px auto;
      font-weight: 600;
    }
  }
  
  
  .custom-checkbox input[type="checkbox"] {
    display: none; /* Hide the default checkbox */
  }
  
  .custom-checkbox label {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }
  
  .custom-checkbox label::before {
    content: '';
    width: 20px; /* Adjust size as needed */
    height: 20px;
    border: 2px solid #000; /* Black border */
    border-radius: 4px; /* Rounded corners (optional) */
    display: inline-block;
    box-sizing: border-box;
    background-color: #fff; /* White background */
    transition: background-color 0.2s, border-color 0.2s;
  }
  
  .custom-checkbox input[type="checkbox"]:checked + label::before {
    background-color: #000; /* Black background for ticked checkbox */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 14px 14px; /* Adjust tick size */
  }
  
  .custom-checkbox a {
    text-decoration: none;
    color: #007BFF; /* Adjust link color */
  }
  
  .custom-checkbox a:hover {
    text-decoration: underline;
  }


  
  .invite-favorites-wrap {
    width: 100%;
    background: url(../../../../../../assets/images/object-light.png) no-repeat 100% 100%;
    padding-block: 131px;
  }
  
  .invite-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 128px 64px 64px;
    gap: 47px;
    width: 1110px;
    min-height: 597px;
    background: #F2F2F2;
    border-radius: 15px;
    margin: 0px auto;
    position: relative;
  
    .dot-matrik {
      position: absolute;
      width: 190px;
      height: 177px;
      top: -35px;
      left: -43px;
      z-index: -1;
      background: url(../../../../../../assets/images/light-dot-pattern.svg) no-repeat center center;
    }
  }
  
  .fc-hdr-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
  
    color: white;
  
    .text-transfer-upparcase {
      text-transform: uppercase;
      font-size: 25px;
    }
  
    .fc-brand-txt {
      font-size: 40px;
      font-weight: 600;
      margin-bottom: 0;
      line-height: 1.5;
    }
  
    p {
      color: #FFFFFF;
      line-height: 30px;
      text-align: center;
      font-weight: 300;
      font-size: 18px;
      margin-top: 1rem;
    }
  }
  
  .fc-join-help-card{
    label{
      font-size: 18px; 
    }
  
    p{
      font-size: 32px;
      line-height: 1.5;
      font-weight: 600;
      color: #000000;
    }
  }
  
  .join-now-btn{
    background-color:#000000;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 21px 46px;
    gap: 10px;
    width: max-content;
    height: 52px;
    border-radius: 200px;
    text-align: center;
    color: white;
    text-decoration: none;
    font-size: 18px;
    margin-top: 2rem;
  }