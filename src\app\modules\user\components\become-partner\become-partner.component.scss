@media (max-width: 600px) {
  .container {
    padding: 10px;
    font-size: 14px;
  }
}

.fc-business-heading-row{
  font-size: 3rem;
  font-weight: 600;
  color: black;
  margin-top:48px;
}
.fc--become-short-register{
  background-color: rgba(250, 206, 73, 0.1);
  padding: 30px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  gap:2rem;
  border-radius: 32px;
  input{    
    box-sizing: border-box;
    position: relative;
    width: 455px;
    height: 65px;    
    background: #FFFFFF;
    border-radius: 12px;
    border: none;
    padding:0px 1rem;
  }
  label{
    font-size:20px;
    color: rgba(0, 0, 0, 0.616);
    font-weight: 300;
  }
  span{
    font-size: 24px;
    color: black;
    font-weight: 500;
  }

    .become-register-btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 32px 64px;
      width: 180px;
      height: 57px;
      background: #014681;
      border-radius: 100px;
      border: none;
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
}


.fc-content-card{
  align-items: center;


  img{
    max-width: 90%;
  }

  label{
    color: black;
  }

  h5{
    margin-bottom: 1rem;
  }

  p{
     line-height: 30px;
     color: black;
  }

  .read-more-btn{    
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 21px 46px;  
  width:max-content;
  height: 52px;
  background: #000000;
  border-radius: 200px;
  color: white;
  text-decoration: none;
  }
}

.fc-full-container{
  background: #12141D;
  position: relative;
  min-height: auto;
  overflow: hidden;
  padding-block:100px;
  display: flex;
  align-items: end;
  
  > div{
    z-index: 1;
    position: relative;
  }
  &::after{
    position: absolute;
    width: 447px;
    height: 443px;
    right:0px;
    top: 3px;
    background: #014681;
    filter: blur(262px);
    content: '';
  }
  
  &::before{
    position: absolute;
    width: 464px;
    height: 468px;
    left: 0px;
    top: 742px;
    background: #014681;
    filter: blur(262px);
    content: '';
  }    


  label{
    font-size: 28px;
    font-weight: 600;
  }

  p{
    font-size: 16px;
    line-height: 30px;
    font-weight: 300;
    margin-top: 1rem;

    b{
      color: #FFA500;
      font-weight: 600;
    }
  }
}




.fc-become-partner-section{
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 80px auto;
  text-align: center;
  gap:2.5rem;
  flex-direction: column;

  // .label-text{
  // }

  .label-text{
    font-size: 24px;
    color: #014681;
  }

  h4{
    font-size: 38px;
    font-weight: 600;
    max-width: 875px;
    margin: 0px auto;
  }
  
  input[type="text"]{  
    width: 609.16px;
    height: 65px;
    background: #F4F4F4;
    border-radius: 12px;
    border: none ;
    padding: 0px 1rem;
  }
  .become-btn{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;  
    gap: 10px;
    width: 253px;  
    height: 57px;
    background: #014681;
    border-radius: 100px;
    color: white;
    font-size: 18px;
    border: none;
    margin: 0px auto;
    font-weight: 600;
  }
}


.custom-checkbox input[type="checkbox"] {
  display: none; /* Hide the default checkbox */
}

.custom-checkbox label {
  display: inline-flex;
  align-items: center;
   cursor: pointer;
   
   a{
   margin-left: 5px;
   }
}

.custom-checkbox label::before {
  content: '';
  width: 20px; /* Adjust size as needed */
  height: 20px;
  border: 2px solid #000; /* Black border */
  border-radius: 4px; /* Rounded corners (optional) */
  display: inline-block;
  box-sizing: border-box;
  background-color: #fff; /* White background */
  transition: background-color 0.2s, border-color 0.2s;
  margin-right:8px;
}

.custom-checkbox input[type="checkbox"]:checked + label::before {
  background-color: #000; /* Black background for ticked checkbox */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 14px 14px; /* Adjust tick size */
}

.custom-checkbox a {
  text-decoration: none;
  color: #007BFF; /* Adjust link color */
}

.custom-checkbox a:hover {
  text-decoration: underline;
}

.pricing-plan-btn{  
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 125px;
    height: 49px;
    background: #014681;
    border-radius: 100px;
    flex: none;
    order: 1;
    flex-grow: 0;
    font-size: 14px;
    color: white;
    text-decoration: none;
    font-weight: bold;
    padding-inline: 1rem;
    width: auto;
}

@media(max-width:768px){
  .fc-full-container{
    padding-block: 2rem;

    p{
      font-size: 14px;
      line-height: 2;
      margin-bottom: 0px;
    }

    h2{
      font-size: 1.25rem;
    }
    h1{
      font-size: 1.25rem;
    }
  }
  .fc-become-partner-section{
    margin: 2rem auto;
    width: 100%;
    flex-wrap: wrap;
    input[type=text]{
      width:100%;
      height:50px;
    }
    h4{
      font-size: 1rem;
      max-width:100%;
      line-height: 1.5;
    }
    .become-btn{
      font-size: 1rem;
      line-height: 1.5;
      height: 45px;
    }
  }
  .custom-checkbox{
    label{
      white-space: nowrap;
      flex-wrap: wrap;
      flex: 1;
    }
  }

  .fc-content-box{
    margin-top: 1rem;

    h4{
      font-size: 1.25rem !important;
      line-height: 1.5;
    }

    ul{
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
  }
}