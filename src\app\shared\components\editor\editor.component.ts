import {
  Component,
  EventEmitter,
  Input,
  Output,
  type OnInit,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import { EditorModule } from '@tinymce/tinymce-angular';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-editor',
  standalone: true,
  imports: [EditorModule, FormsModule],
  templateUrl: './editor.component.html',
  styleUrls: ['./editor.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TinyMCEditorComponent implements OnInit {
  @Output() onChange = new EventEmitter<string>();
  @Input() content: string = '';
  init: any = {
    plugins: 'lists link image table code help wordcount',
  };

  constructor() { }

  ngOnInit(): void {
    this.init = {
      height: 400,
      menubar: true,
      plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
        'codesample', 'quickbars', 'powerpaste', 'textcolor', 'colorpicker'
      ],
      toolbar: 'undo redo | blocks | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help | fullscreen | code',
      toolbar_mode: 'sliding',
      quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
      quickbars_insert_toolbar: 'quickimage quicktable',
      contextmenu: 'link image table',
      skin: 'oxide',
      content_css: 'default',
      branding: false,
      promotion: false,
      resize: true,
      elementpath: false,
      statusbar: true,
      paste_data_images: true,
      images_upload_url: 'https://localhost:44312/api/Blog/UploadBlogImage',
      automatic_uploads: true,
      file_picker_types: 'image',
      // Configure for modal compatibility
      inline: false,
      // This is the key setting - render UI in the document body but with proper z-index
      ui_container: document.body,
      setup: (editor: any) => {
        editor.on('Change', () => {
          const content = editor.getContent();
          this.handleOnChange(content);
        });
        editor.on('KeyUp', () => {
          const content = editor.getContent();
          this.handleOnChange(content);
        });

        // Apply modal-specific configuration
        editor.on('init', () => {
          const editorContainer = editor.getContainer();
          if (editorContainer) {
            const modalContent = editorContainer.closest('.modal-content');
            if (modalContent) {
              // Add a class to identify this as a modal editor
              editorContainer.classList.add('tinymce-in-modal');

              // Apply styles to ensure proper z-index
              const style = document.createElement('style');
              style.id = 'tinymce-modal-fix';
              style.textContent = `
                .tox-tinymce-aux {
                  z-index: 1060 !important;
                }
                .tox-menu {
                  z-index: 1061 !important;
                }
                .tox-collection {
                  z-index: 1062 !important;
                }
                .tox-pop {
                  z-index: 1063 !important;
                }
                .tox-dialog-wrap {
                  z-index: 1064 !important;
                }
                .tox-dialog {
                  z-index: 1065 !important;
                }
              `;

              // Only add the style if it doesn't exist
              if (!document.getElementById('tinymce-modal-fix')) {
                document.head.appendChild(style);
              }
            }
          }
        });
      },
    };
  }

  handleOnChange($event: string) {
    this.onChange.emit($event);
  }
}
