import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  type OnInit,
} from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { CompanyService } from '../../services/company.service';

@Component({
  selector: 'app-expertise',
  templateUrl: './view-expertise.component.html',
  styleUrls: ['./view-expertise.component.scss'],
})
export class ViewExpertiseComponent implements OnInit, OnChanges {
  vm: any = {};
  authorName: string = '';
  @Input() companyId: string = '';
  @Input() userId: string = '';
  @Input() isEndUser: boolean = false;
  @Input() endUserData: any = null;
  @Input() title = 'Expertise';
  constructor(private account: CompanyService) { }
  ngOnInit(): void {
    console.log('Expertise component ngOnInit - isEndUser:', this.isEndUser);
    console.log('Expertise component ngOnInit - endUserData:', this.endUserData);
    console.log('Expertise component ngOnInit - companyId:', this.companyId);

    if (this.isEndUser && this.endUserData) {
      this.loadEndUserExpertise();
    } else if (this.companyId) {
      this.getExpertise(this.companyId);
    } else {
      console.log('No data to load in expertise component');
    }
  }

  ngOnChanges(): void {
    console.log('Expertise component ngOnChanges - isEndUser:', this.isEndUser);
    console.log('Expertise component ngOnChanges - endUserData:', this.endUserData);

    if (this.isEndUser && this.endUserData) {
      this.loadEndUserExpertise();
    }
  }

  loadEndUserExpertise() {
    console.log('Loading end user expertise with data:', this.endUserData);

    // Use the end user data directly instead of making an API call
    this.vm = {
      expertise: this.endUserData.expertise || [],
      industries: this.endUserData.industries || [],
      products: this.endUserData.products || [],
      services: this.endUserData.services || [],
      solutions: this.endUserData.solutions || [],
      companySystem: this.endUserData.companySystem || []
    };
    this.authorName = this.endUserData.organizationName || this.endUserData.companyName || '';

    console.log('Loaded expertise vm:', this.vm);
    console.log('Author name:', this.authorName);
  }

  getExpertise(userId: string) {
    this.account.getExpertise(userId).subscribe((response: any) => {
      this.vm = response.data;
      this.authorName = this.vm.companyName;
    });
  }
}
